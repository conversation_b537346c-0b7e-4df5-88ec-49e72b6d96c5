# 🚀 Setup Instructions for AI-Based Data Cleaner

This guide will help you set up and run the AI-Based Data Cleaner application on your local machine.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.8 or higher** - [Download Python](https://www.python.org/downloads/)
- **pip** (Python package installer) - Usually comes with Python
- **OpenAI API Key** - [Get your API key](https://platform.openai.com/api-keys)

## 🔧 Step-by-Step Setup

### 1. Download the Application

If you received this as a ZIP file:
```bash
# Extract the ZIP file to your desired location
# Navigate to the extracted folder
cd Ai_Based_Data_Cleaner
```

If you're cloning from a repository:
```bash
git clone <repository-url>
cd Ai_Based_Data_Cleaner
```

### 2. Create a Virtual Environment (Recommended)

Creating a virtual environment helps avoid conflicts with other Python projects:

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

Install all required Python packages:

```bash
pip install -r requirements.txt
```

If you encounter any issues, try upgrading pip first:
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Configure Environment Variables

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file:**
   Open the `.env` file in a text editor and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_actual_openai_api_key_here
   MAX_FILE_SIZE_MB=50
   SUPPORTED_FORMATS=csv,xlsx,xls
   LOG_LEVEL=INFO
   ```

3. **Get your OpenAI API Key:**
   - Go to [OpenAI Platform](https://platform.openai.com/api-keys)
   - Sign in or create an account
   - Click "Create new secret key"
   - Copy the key and paste it in your `.env` file

### 5. Test the Installation

Run a quick test to ensure everything is working:

```bash
python -c "import streamlit, pandas, openai; print('All dependencies installed successfully!')"
```

### 6. Run the Application

Start the Streamlit application:

```bash
streamlit run app.py
```

The application should automatically open in your default web browser at `http://localhost:8501`.

## 🧪 Testing with Sample Data

1. **Use the provided sample files:**
   - `examples/sample_data.csv` - Contains various data quality issues
   - Upload this file to test the application

2. **Test different features:**
   - Try uploading the sample CSV file
   - Enable different cleaning options
   - Review the before/after comparison
   - Download the cleaned data

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. "ModuleNotFoundError" when running the app
**Problem:** Missing Python packages
**Solution:**
```bash
pip install -r requirements.txt
```

#### 2. "OpenAI API key not found" error
**Problem:** API key not configured properly
**Solution:**
- Check that your `.env` file exists in the project root
- Verify the API key is correctly set: `OPENAI_API_KEY=sk-...`
- Ensure there are no extra spaces or quotes around the key

#### 3. "Permission denied" errors
**Problem:** File permissions or virtual environment issues
**Solution:**
```bash
# On Windows, run as administrator
# On macOS/Linux:
sudo chmod +x app.py
```

#### 4. Streamlit won't start
**Problem:** Port already in use or Streamlit issues
**Solution:**
```bash
# Try a different port
streamlit run app.py --server.port 8502

# Or kill existing Streamlit processes
pkill -f streamlit
```

#### 5. Large files cause memory errors
**Problem:** Insufficient RAM for large datasets
**Solution:**
- Try with smaller files first
- Increase system memory if possible
- Split large files into smaller chunks

#### 6. AI features not working
**Problem:** OpenAI API issues
**Solution:**
- Check your API key is valid and has credits
- Verify internet connection
- Try disabling AI features temporarily
- Check OpenAI service status

### 7. File upload fails
**Problem:** File format or size issues
**Solution:**
- Ensure file is CSV, XLSX, or XLS format
- Check file size is under 50MB
- Verify file is not corrupted
- Try saving the file in a different format

## 🔧 Advanced Configuration

### Custom Configuration

You can modify `config.py` to customize:

- **AI Model:** Change `OPENAI_MODEL` to use different GPT models
- **Processing Limits:** Adjust `MAX_ROWS_FOR_AI_PROCESSING`
- **API Settings:** Modify `MAX_TOKENS` and `TEMPERATURE`

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `MAX_FILE_SIZE_MB` | Maximum upload size | 50 |
| `SUPPORTED_FORMATS` | Allowed file types | csv,xlsx,xls |
| `LOG_LEVEL` | Logging verbosity | INFO |

### Logging

Logs are stored in the `logs/` directory:
- Check `logs/data_cleaner_YYYYMMDD.log` for detailed information
- Increase log level to DEBUG for more verbose output

## 🚀 Production Deployment

For production deployment, consider:

1. **Use a production WSGI server:**
   ```bash
   pip install gunicorn
   # Note: Streamlit has its own deployment options
   ```

2. **Set up proper environment variables:**
   - Use a secure method to store API keys
   - Configure appropriate file size limits
   - Set up proper logging

3. **Security considerations:**
   - Use HTTPS in production
   - Implement proper authentication if needed
   - Regularly update dependencies

## 📞 Getting Help

If you're still having issues:

1. **Check the logs:** Look in the `logs/` directory for error details
2. **Review the README:** Check the main README.md for additional information
3. **Test with sample data:** Use the provided example files first
4. **Check dependencies:** Ensure all packages are correctly installed
5. **Verify API key:** Test your OpenAI API key independently

## 🎉 Success!

Once everything is working, you should see:
- The Streamlit interface loads without errors
- You can upload files successfully
- The cleaning process completes without issues
- You can download cleaned data

Enjoy using the AI-Based Data Cleaner! 🧹✨
